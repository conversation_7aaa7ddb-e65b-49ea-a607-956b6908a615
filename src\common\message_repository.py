import traceback

from typing import List, Any, Optional
from peewee import Model  # 添加 Peewee Model 导入
from src.config.config import global_config

from src.common.database.database_model import Messages
from src.common.logger import get_logger

logger = get_logger(__name__)


def _model_to_dict(model_instance: Model) -> dict[str, Any]:
    """
    将 Peewee 模型实例转换为字典。
    """
    return model_instance.__data__


def find_messages(
    message_filter: dict[str, Any],
    sort: Optional[List[tuple[str, int]]] = None,
    limit: int = 0,
    limit_mode: str = "latest",
    filter_bot=False,
    filter_command=False,
) -> List[dict[str, Any]]:
    """
    根据提供的过滤器、排序和限制条件查找消息。

    Args:
        message_filter: 查询过滤器字典，键为模型字段名，值为期望值或包含操作符的字典 (例如 {'$gt': value}).
        sort: 排序条件列表，例如 [('time', 1)] (1 for asc, -1 for desc)。仅在 limit 为 0 时生效。
        limit: 返回的最大文档数，0表示不限制。
        limit_mode: 当 limit > 0 时生效。 'earliest' 表示获取最早的记录， 'latest' 表示获取最新的记录（结果仍按时间正序排列）。默认为 'latest'。

    Returns:
        消息字典列表，如果出错则返回空列表。
    """
    try:
        logger.info(f"[DEBUG] find_messages 开始执行，参数: filter={message_filter}, sort={sort}, limit={limit}, limit_mode={limit_mode}, filter_bot={filter_bot}, filter_command={filter_command}")

        query = Messages.select()
        logger.info(f"[DEBUG] 初始查询创建完成")

        # 应用过滤器
        if message_filter:
            logger.info(f"[DEBUG] 开始应用过滤器: {message_filter}")
            conditions = []
            for key, value in message_filter.items():
                logger.info(f"[DEBUG] 处理过滤器键值对: {key} = {value}")
                if hasattr(Messages, key):
                    field = getattr(Messages, key)
                    if isinstance(value, dict):
                        # 处理 MongoDB 风格的操作符
                        logger.info(f"[DEBUG] 处理字典类型值，操作符: {list(value.keys())}")
                        for op, op_value in value.items():
                            if op == "$gt":
                                conditions.append(field > op_value)
                                logger.info(f"[DEBUG] 添加条件: {key} > {op_value}")
                            elif op == "$lt":
                                conditions.append(field < op_value)
                                logger.info(f"[DEBUG] 添加条件: {key} < {op_value}")
                            elif op == "$gte":
                                conditions.append(field >= op_value)
                                logger.info(f"[DEBUG] 添加条件: {key} >= {op_value}")
                            elif op == "$lte":
                                conditions.append(field <= op_value)
                                logger.info(f"[DEBUG] 添加条件: {key} <= {op_value}")
                            elif op == "$ne":
                                conditions.append(field != op_value)
                                logger.info(f"[DEBUG] 添加条件: {key} != {op_value}")
                            elif op == "$in":
                                conditions.append(field.in_(op_value))
                                logger.info(f"[DEBUG] 添加条件: {key} in {op_value}")
                            elif op == "$nin":
                                conditions.append(field.not_in(op_value))
                                logger.info(f"[DEBUG] 添加条件: {key} not in {op_value}")
                            else:
                                logger.warning(f"过滤器中遇到未知操作符 '{op}' (字段: '{key}')。将跳过此操作符。")
                    else:
                        # 直接相等比较
                        conditions.append(field == value)
                        logger.info(f"[DEBUG] 添加条件: {key} == {value}")
                else:
                    logger.warning(f"过滤器键 '{key}' 在 Messages 模型中未找到。将跳过此条件。")

            logger.info(f"[DEBUG] 总共生成了 {len(conditions)} 个查询条件")
            if conditions:
                query = query.where(*conditions)
                logger.info(f"[DEBUG] 查询条件已应用到查询对象")
            else:
                logger.info(f"[DEBUG] 没有有效的查询条件，使用原始查询")

        if filter_bot:
            logger.info(f"[DEBUG] 应用 filter_bot，排除 bot 用户 ID: {global_config.bot.qq_account}")
            query = query.where(Messages.user_id != global_config.bot.qq_account)

        if filter_command:
            logger.info(f"[DEBUG] 应用 filter_command，排除命令消息")
            query = query.where(not Messages.is_command)

        # 在执行查询前，先检查数据库中的总记录数
        total_count = Messages.select().count()
        logger.info(f"[DEBUG] 数据库中总消息数: {total_count}")

        # 打印所有记录
        all_messages = list(Messages.select())
        logger.info(f"[DEBUG] 所有记录数: {len(all_messages)}")
        if all_messages:
            for i, msg in enumerate(all_messages[:10]):
                print(f"[DEBUG]   记录 {i+1}: {msg.__data__}")
                # logger.info(f"[DEBUG]   记录 {i+1}: ID={msg.id}, user_id={msg.user_id}, time={msg.time}, content={msg.content[:50]}...")

        # 检查当前查询条件下的记录数（不包含 limit）
        query_count = query.count()
        logger.info(f"[DEBUG] 应用过滤条件后的消息数: {query_count}")

        if limit > 0:
            logger.info(f"[DEBUG] 应用 limit={limit}, limit_mode={limit_mode}")
            if limit_mode == "earliest":
                # 获取时间最早的 limit 条记录，已经是正序
                logger.info(f"[DEBUG] 使用 earliest 模式，按时间升序排列并限制 {limit} 条")
                query = query.order_by(Messages.time.asc()).limit(limit)
                peewee_results = list(query)
                logger.info(f"[DEBUG] earliest 模式查询结果数量: {len(peewee_results)}")
            else:  # 默认为 'latest'
                # 获取时间最晚的 limit 条记录
                logger.info(f"[DEBUG] 使用 latest 模式，按时间降序排列并限制 {limit} 条")
                query = query.order_by(Messages.time.desc()).limit(limit)
                latest_results_peewee = list(query)
                logger.info(f"[DEBUG] latest 模式原始查询结果数量: {len(latest_results_peewee)}")
                # 将结果按时间正序排列
                peewee_results = sorted(latest_results_peewee, key=lambda msg: msg.time)
                logger.info(f"[DEBUG] latest 模式排序后结果数量: {len(peewee_results)}")
        else:
            logger.info(f"[DEBUG] 无 limit 限制，应用排序参数: {sort}")
            # limit 为 0 时，应用传入的 sort 参数
            if sort:
                logger.info(f"[DEBUG] 开始处理排序参数")
                peewee_sort_terms = []
                for field_name, direction in sort:
                    logger.info(f"[DEBUG] 处理排序字段: {field_name}, 方向: {direction}")
                    if hasattr(Messages, field_name):
                        field = getattr(Messages, field_name)
                        if direction == 1:  # ASC
                            peewee_sort_terms.append(field.asc())
                            logger.info(f"[DEBUG] 添加升序排序: {field_name}")
                        elif direction == -1:  # DESC
                            peewee_sort_terms.append(field.desc())
                            logger.info(f"[DEBUG] 添加降序排序: {field_name}")
                        else:
                            logger.warning(f"字段 '{field_name}' 的排序方向 '{direction}' 无效。将跳过此排序条件。")
                    else:
                        logger.warning(f"排序字段 '{field_name}' 在 Messages 模型中未找到。将跳过此排序条件。")
                logger.info(f"[DEBUG] 总共生成了 {len(peewee_sort_terms)} 个排序条件")
                if peewee_sort_terms:
                    query = query.order_by(*peewee_sort_terms)
                    logger.info(f"[DEBUG] 排序条件已应用到查询")

            logger.info(f"[DEBUG] 执行最终查询...")
            peewee_results = list(query)
            logger.info(f"[DEBUG] 最终查询结果数量: {len(peewee_results)}")

        # 打印前几条结果的基本信息（如果有的话）
        if peewee_results:
            logger.info(f"[DEBUG] 前3条结果预览:")
            for i, msg in enumerate(peewee_results[:10]):
                print(f"[DEBUG]  peewee_results 结果 {i+1}: {msg.__data__}")
                # logger.info(f"[DEBUG]   结果 {i+1}: ID={msg.id}, user_id={msg.user_id}, time={msg.time}, content={msg.content[:50]}...")
        else:
            logger.info(f"[DEBUG] 查询结果为空！")

            # 如果结果为空，尝试一些诊断查询
            logger.info(f"[DEBUG] 开始诊断查询...")

            # 检查是否有任何消息
            any_messages = Messages.select().limit(1)
            any_msg_list = list(any_messages)
            if any_msg_list:
                sample_msg = any_msg_list[0]
                logger.info(f"[DEBUG] 数据库中存在消息，示例: ID={sample_msg.id}, user_id={sample_msg.user_id}, time={sample_msg.time}")
            else:
                logger.info(f"[DEBUG] 数据库中完全没有消息！")

            # 如果有过滤条件，尝试不带过滤条件的查询
            if message_filter or filter_bot or filter_command:
                logger.info(f"[DEBUG] 尝试不带任何过滤条件的查询...")
                no_filter_query = Messages.select()
                no_filter_results = list(no_filter_query.limit(5))
                logger.info(f"[DEBUG] 无过滤条件查询结果数量: {len(no_filter_results)}")
                if no_filter_results:
                    for i, msg in enumerate(no_filter_results):
                        logger.info(f"[DEBUG]   无过滤结果 {i+1}: ID={msg.id}, user_id={msg.user_id}, time={msg.time}")

        logger.info(f"[DEBUG] 开始转换为字典格式...")
        result_dicts = [_model_to_dict(msg) for msg in peewee_results]
        logger.info(f"[DEBUG] 转换完成，返回 {len(result_dicts)} 条记录")

        return result_dicts
    except Exception as e:
        log_message = (
            f"[ERROR] 使用 Peewee 查找消息失败 (filter={message_filter}, sort={sort}, limit={limit}, limit_mode={limit_mode}, filter_bot={filter_bot}, filter_command={filter_command}): {e}\n"
            + traceback.format_exc()
        )
        logger.error(log_message)
        return []


def count_messages(message_filter: dict[str, Any]) -> int:
    """
    根据提供的过滤器计算消息数量。

    Args:
        message_filter: 查询过滤器字典，键为模型字段名，值为期望值或包含操作符的字典 (例如 {'$gt': value}).

    Returns:
        符合条件的消息数量，如果出错则返回 0。
    """
    try:
        query = Messages.select()

        # 应用过滤器
        if message_filter:
            conditions = []
            for key, value in message_filter.items():
                if hasattr(Messages, key):
                    field = getattr(Messages, key)
                    if isinstance(value, dict):
                        # 处理 MongoDB 风格的操作符
                        for op, op_value in value.items():
                            if op == "$gt":
                                conditions.append(field > op_value)
                            elif op == "$lt":
                                conditions.append(field < op_value)
                            elif op == "$gte":
                                conditions.append(field >= op_value)
                            elif op == "$lte":
                                conditions.append(field <= op_value)
                            elif op == "$ne":
                                conditions.append(field != op_value)
                            elif op == "$in":
                                conditions.append(field.in_(op_value))
                            elif op == "$nin":
                                conditions.append(field.not_in(op_value))
                            else:
                                logger.warning(
                                    f"计数时，过滤器中遇到未知操作符 '{op}' (字段: '{key}')。将跳过此操作符。"
                                )
                    else:
                        # 直接相等比较
                        conditions.append(field == value)
                else:
                    logger.warning(f"计数时，过滤器键 '{key}' 在 Messages 模型中未找到。将跳过此条件。")
            if conditions:
                query = query.where(*conditions)

        count = query.count()
        return count
    except Exception as e:
        log_message = f"使用 Peewee 计数消息失败 (message_filter={message_filter}): {e}\n{traceback.format_exc()}"
        logger.error(log_message)
        return 0


# 你可以在这里添加更多与 messages 集合相关的数据库操作函数，例如 find_one_message, insert_message 等。
# 注意：对于 Peewee，插入操作通常是 Messages.create(...) 或 instance.save()。
# 查找单个消息可以是 Messages.get_or_none(...) 或 query.first()。
